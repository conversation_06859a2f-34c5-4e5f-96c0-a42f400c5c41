import logging

from app.crawlers.douyin.core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.db.aweme_analysis import save_aweme_analysis

log = logging.getLogger(__name__)


class DouyinSearch:
    def __init__(self):
        self.dy_crawler = DouYinCrawler()
        self.dy_crawler.start()

    def search(self, keyword):
        search_result = self.dy_crawler.search(keyword)
        aweme_analysis = save_aweme_analysis(keyword, search_result, {})
        return aweme_analysis

    # 消毁执行
    def __del__(self):
        self.dy_crawler.close()
