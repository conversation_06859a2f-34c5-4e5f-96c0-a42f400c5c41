import logging
import sys
from time import sleep

from playwright.sync_api import <PERSON>rowserContext, Page
from tenacity import RetryError, retry, retry_if_result, stop_after_attempt, wait_fixed

from app.tools import utils

logger = logging.getLogger(__name__)


class DouYinLogin:
    def __init__(
        self,
        browser_context: BrowserContext,
        context_page: Page,
    ):
        self.browser_context = browser_context
        self.context_page = context_page
        self.scan_qrcode_time = 60

    def begin(self):
        self.popup_login_dialog()

        # check login state
        logger.info("[DouYinLogin.begin] login finished then check login state ...")
        try:
            self.check_login_state()
        except RetryError:
            logger.info("[DouYinLogin.begin] login failed please confirm ...")
            sys.exit()

        # wait for redirect
        wait_redirect_seconds = 5
        logger.info(
            f"[DouYinLogin.begin] Login successful then wait for {wait_redirect_seconds} seconds redirect ..."
        )
        sleep(wait_redirect_seconds)

    @retry(
        stop=stop_after_attempt(600),
        wait=wait_fixed(1),
        retry=retry_if_result(lambda value: value is False),
    )
    def check_login_state(self):
        """Check if the current login status is successful and return True otherwise return False"""
        current_cookie = self.browser_context.cookies()
        _, cookie_dict = utils.convert_cookies(current_cookie)

        for page in self.browser_context.pages:
            try:
                local_storage = page.evaluate("() => window.localStorage")
                if local_storage.get("HasUserLogin", "") == "1":
                    return True
            except Exception:
                # utils.logger.warn(f"[DouYinLogin] check_login_state waring: {e}")
                sleep(0.1)

        if cookie_dict.get("LOGIN_STATUS") == "1":
            return True

        return False

    def popup_login_dialog(self):
        """If the login dialog box does not pop up automatically, we will manually click the login button"""
        dialog_selector = "xpath=//div[@id='login-panel-new']"
        try:
            # check dialog box is auto popup and wait for 10 seconds
            self.context_page.wait_for_selector(dialog_selector, timeout=1000 * 10)
        except Exception as e:
            logger.error(
                f"[DouYinLogin.popup_login_dialog] login dialog box does not pop up automatically, error: {e}"
            )
            logger.info(
                "[DouYinLogin.popup_login_dialog] login dialog box does not pop up automatically, we will manually click the login button"
            )
            login_button_ele = self.context_page.locator("xpath=//p[text() = '登录']")
            login_button_ele.click()
            sleep(0.5)
