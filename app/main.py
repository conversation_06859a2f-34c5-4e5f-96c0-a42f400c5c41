import logging
import os
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from sqlmodel import Session

from app.agents.analysis_agent import (
    analyze_video_first_5_seconds_moviepy,
    batch_analyze_videos,
    get_video_info,
)
from app.db.aweme_analysis import AwemeAnalysis, save_aweme_analysis
from app.db.database import get_session
from app.service.dy import DouyinSearch

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("FastAPI 应用启动")
    yield
    logger.info("FastAPI 应用关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title="作品分析 API",
    description="基于抖音搜索和视频分析的作品分析接口",
    version="1.0.0",
    lifespan=lifespan,
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Pydantic 模型定义
class SearchRequest(BaseModel):
    keyword: str


class VideoAnalysisRequest(BaseModel):
    video_path: str
    question: Optional[str] = "请描述这个视频的前5秒内容"


class BatchVideoAnalysisRequest(BaseModel):
    video_paths: List[str]
    question: Optional[str] = "请描述这个视频的前5秒内容"


class SearchResponse(BaseModel):
    id: int
    keyword: str
    search_result: Optional[List[Dict]]
    analysis_result: Optional[Dict]
    created_at: str


class VideoAnalysisResponse(BaseModel):
    success: bool
    result: Optional[str] = None
    error: Optional[str] = None
    video_info: Optional[Dict] = None


class BatchVideoAnalysisResponse(BaseModel):
    results: Dict[str, VideoAnalysisResponse]


# API 路由
@app.get("/")
async def root():
    """根路径"""
    return {"message": "作品分析 API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "message": "服务运行正常"}


@app.post("/search", response_model=SearchResponse)
async def search_douyin(
    request: SearchRequest,
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session),
):
    """
    抖音关键词搜索接口

    Args:
        request: 搜索请求，包含关键词
        background_tasks: 后台任务
        session: 数据库会话

    Returns:
        搜索结果和分析结果
    """
    try:
        logger.info(f"开始搜索关键词: {request.keyword}")

        # 执行搜索
        douyin_search = DouyinSearch()
        aweme_analysis = douyin_search.search(request.keyword)

        # 返回结果
        return SearchResponse(
            id=aweme_analysis.id,
            keyword=aweme_analysis.keyword,
            search_result=aweme_analysis.search_result,
            analysis_result=aweme_analysis.analysis_result,
            created_at=aweme_analysis.created_at.isoformat(),
        )

    except Exception as e:
        logger.error(f"搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@app.post("/analyze/video", response_model=VideoAnalysisResponse)
async def analyze_video(request: VideoAnalysisRequest):
    """
    单个视频分析接口

    Args:
        request: 视频分析请求

    Returns:
        视频分析结果
    """
    try:
        logger.info(f"开始分析视频: {request.video_path}")

        # 检查文件是否存在
        if not os.path.exists(request.video_path):
            raise HTTPException(
                status_code=404, detail=f"视频文件不存在: {request.video_path}"
            )

        # 获取视频信息
        try:
            video_info = get_video_info(request.video_path)
        except Exception as e:
            logger.warning(f"获取视频信息失败: {e}")
            video_info = None

        # 分析视频
        result = analyze_video_first_5_seconds_moviepy(
            request.video_path, request.question
        )

        return VideoAnalysisResponse(success=True, result=result, video_info=video_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"视频分析失败: {str(e)}")
        return VideoAnalysisResponse(success=False, error=str(e))


@app.post("/analyze/videos/batch", response_model=BatchVideoAnalysisResponse)
async def analyze_videos_batch(request: BatchVideoAnalysisRequest):
    """
    批量视频分析接口

    Args:
        request: 批量视频分析请求

    Returns:
        批量视频分析结果
    """
    try:
        logger.info(f"开始批量分析 {len(request.video_paths)} 个视频")

        # 检查所有文件是否存在
        missing_files = []
        for video_path in request.video_paths:
            if not os.path.exists(video_path):
                missing_files.append(video_path)

        if missing_files:
            raise HTTPException(
                status_code=404, detail=f"以下视频文件不存在: {missing_files}"
            )

        # 批量分析视频
        results = batch_analyze_videos(request.video_paths, request.question)

        # 转换结果格式
        formatted_results = {}
        for video_path, result in results.items():
            if result["success"]:
                formatted_results[video_path] = VideoAnalysisResponse(
                    success=True, result=result["result"]
                )
            else:
                formatted_results[video_path] = VideoAnalysisResponse(
                    success=False, error=result["error"]
                )

        return BatchVideoAnalysisResponse(results=formatted_results)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量视频分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量视频分析失败: {str(e)}")


@app.get("/search/history")
async def get_search_history(
    limit: int = 10, offset: int = 0, session: Session = Depends(get_session)
):
    """
    获取搜索历史

    Args:
        limit: 返回数量限制
        offset: 偏移量
        session: 数据库会话

    Returns:
        搜索历史列表
    """
    try:
        # 查询搜索历史
        from sqlmodel import select

        statement = select(AwemeAnalysis).offset(offset).limit(limit)
        results = session.exec(statement).all()

        history = []
        for result in results:
            history.append(
                {
                    "id": result.id,
                    "keyword": result.keyword,
                    "created_at": result.created_at.isoformat(),
                    "has_search_result": result.search_result is not None,
                    "has_analysis_result": result.analysis_result is not None,
                }
            )

        return {"history": history, "total": len(history)}

    except Exception as e:
        logger.error(f"获取搜索历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取搜索历史失败: {str(e)}")


@app.get("/search/{search_id}")
async def get_search_result(search_id: int, session: Session = Depends(get_session)):
    """
    根据 ID 获取搜索结果

    Args:
        search_id: 搜索记录 ID
        session: 数据库会话

    Returns:
        搜索结果详情
    """
    try:
        # 查询搜索结果
        result = session.get(AwemeAnalysis, search_id)

        if not result:
            raise HTTPException(status_code=404, detail="搜索记录不存在")

        return SearchResponse(
            id=result.id,
            keyword=result.keyword,
            search_result=result.search_result,
            analysis_result=result.analysis_result,
            created_at=result.created_at.isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取搜索结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取搜索结果失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    # 启动服务器
    uvicorn.run(
        "app.main:app", host="0.0.0.0", port=8000, reload=True, log_level="info"
    )
