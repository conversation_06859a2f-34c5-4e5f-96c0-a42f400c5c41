import logging
from datetime import datetime
from typing import Any, Dict

from sqlmodel import JSON, Column, Field, Session, SQLModel

from app.db.database import engine

log = logging.getLogger(__name__)


class AwemeAnalysis(SQLModel, table=True):
    __tablename__ = "aweme_analysis"

    id: int | None = Field(default=None, primary_key=True, description="主键")
    keyword: str = Field(max_length=500, description="关键词")
    search_result: list[Dict[str, Any]] | None = Field(
        default=None, sa_column=Column(JSON), description="搜索结果"
    )
    analysis_result: Dict[str, Any] | None = Field(
        default=None, sa_column=Column(JSON), description="分析结果"
    )
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


def save_aweme_analysis(keyword: str, search_result: list, analysis_result: dict):
    try:
        with Session(engine) as session:
            aweme_analysis = AwemeAnalysis(
                keyword=keyword,
                search_result=search_result,
                analysis_result=analysis_result,
            )
            session.add(aweme_analysis)
            session.commit()
            session.refresh(aweme_analysis)
            log.info(f"分析结果已保存到数据库，关键词: {keyword}")
            return aweme_analysis
    except Exception as e:
        log.error(f"保存搜索结果失败: {e}")
