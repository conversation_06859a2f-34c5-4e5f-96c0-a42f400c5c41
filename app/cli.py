import logging

import click

from app.service.search import DouyinSearch

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


@click.group()
def cli():
    pass


@cli.command("search")
@click.option("--keyword", type=str, help="关键词")
def open_url(keyword: str):
    DouyinSearch().search(keyword)


if __name__ == "__main__":
    cli()
