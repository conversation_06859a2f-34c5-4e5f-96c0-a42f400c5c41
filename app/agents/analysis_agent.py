import base64
import os
import tempfile
from pathlib import Path

from moviepy import VideoFileClip
from openai import OpenAI

client = OpenAI(
    # 若没有配置环境变量，请用阿里云百炼API Key将下行替换为：api_key="sk-xxx",
    api_key=os.getenv("AI_API_KEY"),
    base_url=os.getenv("AI_BASE_URL"),
)


def extract_first_5_seconds_moviepy(video_path: str) -> str:
    """
    使用 MoviePy 提取视频的前5秒并保存为临时文件

    Args:
        video_path: 原始视频文件路径

    Returns:
        str: 前5秒视频片段的临时文件路径
    """
    # 创建临时文件
    temp_dir = tempfile.gettempdir()
    temp_filename = f"video_first_5s_{os.getpid()}.mp4"
    temp_path = os.path.join(temp_dir, temp_filename)

    try:
        print(f"正在加载视频文件: {video_path}")
        # 加载视频文件
        with VideoFileClip(video_path) as video:
            # 获取视频时长
            duration = video.duration
            print(f"视频总时长: {duration:.2f} 秒")

            # 确定提取时长（最多5秒）
            extract_duration = min(5.0, duration)
            print(f"将提取前 {extract_duration:.2f} 秒")

            # 提取前5秒
            clip = video.subclipped(0, extract_duration)

            # 保存到临时文件
            print(f"正在保存到临时文件: {temp_path}")
            clip.write_videofile(
                temp_path,
                codec="libx264",
                audio_codec="aac",
            )

        return temp_path

    except Exception as e:
        # 如果出错，清理可能创建的临时文件
        if os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except:
                pass
        raise Exception(f"MoviePy 处理视频失败: {str(e)}")


def encode_video(video_path):
    """Base64 编码视频文件"""
    with open(video_path, "rb") as video_file:
        return base64.b64encode(video_file.read()).decode("utf-8")


def get_video_info(video_path: str) -> dict:
    """
    获取视频基本信息

    Args:
        video_path: 视频文件路径

    Returns:
        dict: 包含视频信息的字典
    """
    try:
        with VideoFileClip(video_path) as video:
            info = {
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "width": video.w,
                "height": video.h,
                "has_audio": video.audio is not None,
            }
            return info
    except Exception as e:
        raise Exception(f"获取视频信息失败: {str(e)}")


def analyze_video_first_5_seconds_moviepy(
    video_path: str, question: str = "请描述这个视频的前5秒内容"
) -> str:
    """
    使用 MoviePy 分析视频前5秒的内容

    Args:
        video_path: 视频文件路径
        question: 要问的问题

    Returns:
        str: 模型的分析结果
    """
    temp_video_path = None
    try:
        # 检查原视频文件是否存在
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        # 获取视频信息
        try:
            video_info = get_video_info(video_path)
            print(
                f"视频信息: {video_info['width']}x{video_info['height']}, "
                f"{video_info['duration']:.2f}秒, {video_info['fps']:.1f}fps"
            )
        except Exception as e:
            print(f"获取视频信息时出现警告: {e}")

        print(f"正在使用 MoviePy 提取视频前5秒: {video_path}")
        # 提取前5秒
        temp_video_path = extract_first_5_seconds_moviepy(video_path)
        print(f"前5秒视频片段已保存到: {temp_video_path}")

        # 编码前5秒视频
        print("正在编码视频...")
        base64_video = encode_video(temp_video_path)

        print("正在发送给模型分析...")
        # 发送给模型分析
        completion = client.chat.completions.create(
            model=os.getenv("AI_MODEL"),
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video_url",
                            "video_url": {"url": f"data:;base64,{base64_video}"},
                        },
                        {"type": "text", "text": question},
                    ],
                },
            ],
            # 设置输出数据的模态，当前支持两种：["text","audio"]、["text"]
            modalities=["text"],
            # stream 必须设置为 True，否则会报错
            stream=True,
            stream_options={"include_usage": True},
        )

        # 收集回复内容
        response_text = ""
        for chunk in completion:
            if chunk.choices:
                delta_content = chunk.choices[0].delta.content
                if delta_content:
                    print(delta_content, end="")
                    response_text += delta_content
            else:
                print(f"\n使用情况: {chunk.usage}")

        return response_text

    finally:
        # 清理临时文件
        if temp_video_path and os.path.exists(temp_video_path):
            try:
                os.remove(temp_video_path)
                print(f"\n临时文件已清理: {temp_video_path}")
            except Exception as e:
                print(f"\n清理临时文件失败: {e}")


def batch_analyze_videos(
    video_paths: list, question: str = "请描述这个视频的前5秒内容"
) -> dict:
    """
    批量分析多个视频的前5秒

    Args:
        video_paths: 视频文件路径列表
        question: 要问的问题

    Returns:
        dict: 每个视频的分析结果
    """
    results = {}

    for i, video_path in enumerate(video_paths, 1):
        print(f"\n{'=' * 60}")
        print(f"正在处理第 {i}/{len(video_paths)} 个视频: {video_path}")
        print(f"{'=' * 60}")

        try:
            result = analyze_video_first_5_seconds_moviepy(video_path, question)
            results[video_path] = {"success": True, "result": result}
        except Exception as e:
            print(f"处理视频失败: {e}")
            results[video_path] = {"success": False, "error": str(e)}

    return results


if __name__ == "__main__":
    # 示例使用
    video_path = "D:\\code\\jarvis\\spring_mountain.mp4"
    # 分析视频的前5秒
    try:
        result = analyze_video_first_5_seconds_moviepy(
            video_path, "请分析这个视频，包括视觉元素、声音内容和主要事件"
        )
        print(f"\n\n分析结果:\n{result}")
    except Exception as e:
        print(f"分析失败: {e}")

    # 示例：批量处理多个视频
    # video_list = ["video1.mp4", "video2.mp4", "video3.mp4"]
    # results = batch_analyze_videos(video_list, "这个视频前5秒的主要内容是什么？")
    # for video, result in results.items():
    #     print(f"\n{video}: {result}")
