# 作品分析 API 文档

基于 FastAPI 和 uvicorn 实现的作品分析接口，提供抖音搜索和视频分析功能。

## 启动服务

### 方法一：直接运行
```bash
python run_server.py
```

### 方法二：使用 uvicorn 命令
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 方法三：使用 Python 模块
```bash
python -m app.main
```

服务启动后，可以通过以下地址访问：
- API 服务：http://localhost:8000
- 交互式文档：http://localhost:8000/docs
- ReDoc 文档：http://localhost:8000/redoc

## API 接口

### 1. 基础接口

#### GET `/` - 根路径
返回 API 基本信息

**响应示例：**
```json
{
  "message": "作品分析 API",
  "version": "1.0.0"
}
```

#### GET `/health` - 健康检查
检查服务状态

**响应示例：**
```json
{
  "status": "healthy",
  "message": "服务运行正常"
}
```

### 2. 抖音搜索接口

#### POST `/search` - 抖音关键词搜索
根据关键词搜索抖音内容并保存到数据库

**请求体：**
```json
{
  "keyword": "九三阅兵观后感"
}
```

**响应示例：**
```json
{
  "id": 1,
  "keyword": "九三阅兵观后感",
  "search_result": [...],
  "analysis_result": {...},
  "created_at": "2024-01-01T12:00:00"
}
```

#### GET `/search/history` - 获取搜索历史
获取历史搜索记录列表

**查询参数：**
- `limit`: 返回数量限制（默认：10）
- `offset`: 偏移量（默认：0）

**响应示例：**
```json
{
  "history": [
    {
      "id": 1,
      "keyword": "九三阅兵观后感",
      "created_at": "2024-01-01T12:00:00",
      "has_search_result": true,
      "has_analysis_result": true
    }
  ],
  "total": 1
}
```

#### GET `/search/{search_id}` - 获取搜索结果详情
根据搜索记录 ID 获取详细结果

**路径参数：**
- `search_id`: 搜索记录 ID

**响应示例：**
```json
{
  "id": 1,
  "keyword": "九三阅兵观后感",
  "search_result": [...],
  "analysis_result": {...},
  "created_at": "2024-01-01T12:00:00"
}
```

### 3. 视频分析接口

#### POST `/analyze/video` - 单个视频分析
分析单个视频文件的前5秒内容

**请求体：**
```json
{
  "video_path": "/path/to/video.mp4",
  "question": "请描述这个视频的前5秒内容"
}
```

**响应示例：**
```json
{
  "success": true,
  "result": "视频分析结果...",
  "error": null,
  "video_info": {
    "duration": 30.5,
    "fps": 30.0,
    "size": [1920, 1080],
    "width": 1920,
    "height": 1080,
    "has_audio": true
  }
}
```

#### POST `/analyze/videos/batch` - 批量视频分析
批量分析多个视频文件

**请求体：**
```json
{
  "video_paths": [
    "/path/to/video1.mp4",
    "/path/to/video2.mp4"
  ],
  "question": "请描述这个视频的前5秒内容"
}
```

**响应示例：**
```json
{
  "results": {
    "/path/to/video1.mp4": {
      "success": true,
      "result": "第一个视频的分析结果...",
      "error": null,
      "video_info": null
    },
    "/path/to/video2.mp4": {
      "success": false,
      "result": null,
      "error": "分析失败的错误信息",
      "video_info": null
    }
  }
}
```

## 使用示例

### Python 客户端示例

```python
import requests
import json

# API 基础 URL
BASE_URL = "http://localhost:8000"

# 1. 健康检查
response = requests.get(f"{BASE_URL}/health")
print("健康检查:", response.json())

# 2. 抖音搜索
search_data = {"keyword": "九三阅兵观后感"}
response = requests.post(f"{BASE_URL}/search", json=search_data)
print("搜索结果:", response.json())

# 3. 视频分析
video_data = {
    "video_path": "/path/to/your/video.mp4",
    "question": "请分析这个视频的内容"
}
response = requests.post(f"{BASE_URL}/analyze/video", json=video_data)
print("视频分析:", response.json())

# 4. 获取搜索历史
response = requests.get(f"{BASE_URL}/search/history?limit=5")
print("搜索历史:", response.json())
```

### curl 示例

```bash
# 健康检查
curl -X GET "http://localhost:8000/health"

# 抖音搜索
curl -X POST "http://localhost:8000/search" \
  -H "Content-Type: application/json" \
  -d '{"keyword": "九三阅兵观后感"}'

# 视频分析
curl -X POST "http://localhost:8000/analyze/video" \
  -H "Content-Type: application/json" \
  -d '{"video_path": "/path/to/video.mp4", "question": "请描述视频内容"}'

# 获取搜索历史
curl -X GET "http://localhost:8000/search/history?limit=10&offset=0"
```

## 错误处理

API 使用标准的 HTTP 状态码：

- `200`: 成功
- `404`: 资源不存在（如视频文件不存在、搜索记录不存在）
- `422`: 请求参数验证失败
- `500`: 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```

## 注意事项

1. **环境变量配置**：确保已正确配置以下环境变量：
   - `AI_API_KEY`: AI 模型 API 密钥
   - `AI_BASE_URL`: AI 模型 API 基础 URL
   - `AI_MODEL`: AI 模型名称
   - `MYSQL_*`: 数据库连接配置

2. **文件路径**：视频分析接口需要提供服务器上的绝对文件路径

3. **性能考虑**：
   - 视频分析是计算密集型操作，建议合理控制并发请求
   - 批量视频分析会按顺序处理，耗时较长

4. **CORS 配置**：当前配置允许所有来源的跨域请求，生产环境中应该限制具体域名

## 开发和调试

启动开发服务器（带自动重载）：
```bash
python run_server.py
```

查看详细的 API 文档：
- 访问 http://localhost:8000/docs 查看 Swagger UI
- 访问 http://localhost:8000/redoc 查看 ReDoc 文档
