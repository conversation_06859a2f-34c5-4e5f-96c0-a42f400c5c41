[project]
name = "jarvis"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "click>=8.2.1",
    "debugpy>=1.8.14",
    "httpx>=0.28.1",
    "moviepy>=2.2.1",
    "openai>=1.105.0",
    "playwright>=1.52.0",
    "playwright-stealth>=2.0.0",
    "pyexecjs>=1.5.1",
    "pymysql>=1.1.2",
    "python-dotenv>=1.1.0",
    "requests>=2.32.4",
    "sqlmodel>=0.0.24",
    "tenacity>=9.1.2",
]

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
default = true

[project.optional-dependencies]
dev = ["mypy>=1.16.1", "ruff>=0.11.13"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    # "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    # We actually do want to import from typing_extensions
    "UP035",
    # Relax the convention by _not_ requiring documentation for every function parameter.
    "D417",
    "E501",
]
[tool.ruff.lint.pydocstyle]
convention = "google"

