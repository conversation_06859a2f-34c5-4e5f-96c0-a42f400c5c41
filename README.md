# Jarvis - 作品分析系统

基于 FastAPI 和 uvicorn 实现的智能作品分析系统，提供抖音内容搜索和视频分析功能。

## 功能特性

- 🔍 **抖音搜索**: 根据关键词搜索抖音内容
- 🎥 **视频分析**: 使用 AI 模型分析视频内容
- 📊 **批量处理**: 支持批量视频分析
- 💾 **数据存储**: 搜索结果和分析结果持久化存储
- 🌐 **RESTful API**: 完整的 REST API 接口
- 📖 **自动文档**: 自动生成的 API 文档

## 快速开始

### 1. 环境准备

确保已安装 Python 3.13+ 和 uv 包管理器。

### 2. 安装依赖

```bash
# 安装项目依赖
uv sync

# 或者使用 pip
pip install -e .
```

### 3. 环境配置

创建 `.env` 文件并配置必要的环境变量：

```bash
# AI 模型配置
AI_API_KEY=your_ai_api_key
AI_BASE_URL=your_ai_base_url
AI_MODEL=your_ai_model_name

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database
```

### 4. 启动服务

```bash
# 方法一：使用启动脚本
python run_server.py

# 方法二：直接使用 uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 方法三：使用 Python 模块
python -m app.main
```

### 5. 访问服务

- **API 服务**: http://localhost:8000
- **交互式文档**: http://localhost:8000/docs
- **ReDoc 文档**: http://localhost:8000/redoc

## API 接口

### 基础接口

- `GET /` - 获取 API 基本信息
- `GET /health` - 健康检查

### 搜索接口

- `POST /search` - 抖音关键词搜索
- `GET /search/history` - 获取搜索历史
- `GET /search/{search_id}` - 获取搜索结果详情

### 视频分析接口

- `POST /analyze/video` - 单个视频分析
- `POST /analyze/videos/batch` - 批量视频分析

详细的 API 文档请查看 [API_DOCS.md](API_DOCS.md)

## 使用示例

### Python 客户端

```python
import requests

# 健康检查
response = requests.get("http://localhost:8000/health")
print(response.json())

# 抖音搜索
search_data = {"keyword": "九三阅兵观后感"}
response = requests.post("http://localhost:8000/search", json=search_data)
print(response.json())

# 视频分析
video_data = {
    "video_path": "/path/to/video.mp4",
    "question": "请描述这个视频的内容"
}
response = requests.post("http://localhost:8000/analyze/video", json=video_data)
print(response.json())
```

### curl 命令

```bash
# 健康检查
curl -X GET "http://localhost:8000/health"

# 抖音搜索
curl -X POST "http://localhost:8000/search" \
  -H "Content-Type: application/json" \
  -d '{"keyword": "九三阅兵观后感"}'
```

## 测试

运行测试脚本验证 API 功能：

```bash
python test_api.py
```

## 命令行工具

除了 Web API，还提供命令行工具：

```bash
# 抖音搜索
python -m app.cli search --keyword "九三阅兵观后感"

# 视频分析
python -m app.agents.analysis_agent
```

## 项目结构

```
jarvis/
├── app/
│   ├── agents/          # AI 分析代理
│   ├── crawlers/        # 爬虫模块
│   ├── db/             # 数据库模型
│   ├── service/        # 业务服务
│   ├── tools/          # 工具函数
│   ├── cli.py          # 命令行接口
│   └── main.py         # FastAPI 应用
├── browser_data/       # 浏览器数据
├── libs/              # 外部库
├── resource/          # 资源文件
├── run_server.py      # 服务启动脚本
├── test_api.py        # API 测试脚本
├── API_DOCS.md        # API 详细文档
└── README.md          # 项目说明
```

## 开发

### 代码格式化

```bash
# 使用 ruff 格式化代码
uv run ruff format .

# 检查代码质量
uv run ruff check .
```

### 类型检查

```bash
# 使用 mypy 进行类型检查
uv run mypy app/
```

## 注意事项

1. **环境变量**: 确保正确配置所有必需的环境变量
2. **文件路径**: 视频分析需要提供服务器上的绝对文件路径
3. **性能**: 视频分析是计算密集型操作，建议合理控制并发
4. **数据库**: 确保数据库服务正常运行并已创建相应的表

## 许可证

[添加许可证信息]

## 贡献

欢迎提交 Issue 和 Pull Request！